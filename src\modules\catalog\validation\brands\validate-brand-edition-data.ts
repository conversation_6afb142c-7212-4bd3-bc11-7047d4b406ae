import { CustomError } from "@/utils/custom-error";

export function validateBrandEditionData(formData: FormData): void {
  const name = formData.get("name") as string;
  const displayOrder = formData.get("displayOrder") as string;

  if (!name || name.trim() === "") {
    throw new CustomError("Invalid Data!", 400);
  }

  if (displayOrder && displayOrder.trim() !== "") {
    const orderNumber = parseInt(displayOrder, 10);
    if (isNaN(orderNumber) || orderNumber < 1) {
      throw new CustomError("Display order must be a positive number!", 400);
    }
  }
}
