import { CustomError } from "@/utils/custom-error";

export function validateProductEditionData(
  submittedData: Record<string, any>
): void {
  if (!submittedData.content || submittedData.content.trim() === "") {
    throw new CustomError("Missed Data!", 400);
  }

  if (
    !submittedData.categoryIds ||
    (Array.isArray(submittedData.categoryIds) &&
      submittedData.categoryIds.length === 0) ||
    (typeof submittedData.categoryIds === "string" &&
      submittedData.categoryIds === "")
  ) {
    throw new CustomError("Missed Data!", 400);
  }

  if (submittedData.displayOrder && submittedData.displayOrder.trim() !== "") {
    const orderNumber = parseInt(submittedData.displayOrder, 10);
    if (isNaN(orderNumber) || orderNumber < 1) {
      throw new CustomError("Invalid Data!", 400);
    }
  }
}
