import { CustomError } from "@/utils/custom-error";

export function validateItemData(formData: FormData): void {
  const requiredFields: string[] = ["barcode", "image", "price"];
  const missingFields: string[] = [];

  requiredFields.forEach((field) => {
    if (!formData.has(field) || formData.get(field) === "") {
      missingFields.push(field);
    }
  });

  if (missingFields.length > 0) {
    throw new CustomError(`Missed Data!`, 400);
  }

  const image = formData.get("image");
  const defaultImage = formData.get("defaultImage");

  if (
    (!defaultImage || defaultImage === "") && //user remove the default image in the edition without adding an image (no previewed image)
    image instanceof File &&
    image.size === 0 //no image is uploaded
  ) {
    throw new CustomError("Missed Data!", 400);
  } else if (image instanceof File && image.size === 0)
    formData.delete("image");

  formData.delete("defaultImage");

  const price = formData.get("price");
  if (price !== null && typeof price === "string") {
    if (parseFloat(price) < 0) {
      throw new CustomError(`Invalid Data!`, 400);
    }
  }

  const quantity = formData.get("quantity");
  if (quantity !== null && typeof quantity === "string") {
    if (parseFloat(quantity) < 0) {
      throw new CustomError(`Invalid Data`, 400);
    }
  }
}
