import { CustomError } from "@/utils/custom-error";

export function validateProductCreationData(formData: FormData): void {
  const requiredLanguages = ["Arabic", "French", "English"];
  const missingFields: string[] = [];

  const contentField = formData.get("content");
  if (contentField && typeof contentField === "string") {
    try {
      const content = JSON.parse(contentField);
      if (Array.isArray(content)) {
        requiredLanguages.forEach((lang) => {
          const langContent = content.find((item) => item.language === lang);
          if (
            !langContent ||
            !langContent.name ||
            langContent.name.trim() === ""
          ) {
            missingFields.push(`${lang} name`);
          }
        });
      } else {
        throw new CustomError(`Invalid Data!`, 400);
      }
    } catch (error) {
      throw new CustomError(`Invalid Data!`, 400);
    }
  } else {
    throw new CustomError(`Missed Data!`, 400);
  }

  const categoryIds = formData.getAll("categoryIds");
  if (!categoryIds || categoryIds.length === 0 || categoryIds[0] === "") {
    missingFields.push("categoryIds");
  }

  if (missingFields.length > 0) {
    throw new CustomError(`Missed Data!`, 400);
  }

  const displayOrder = formData.get("displayOrder");
  if (displayOrder !== null && typeof displayOrder === "string") {
    if (parseFloat(displayOrder) < 1) {
      throw new CustomError(`Invalid Data!`, 400);
    }
  }
}
